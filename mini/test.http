
### Get all employees
GET http://localhost:4004/odata/v4/mini/Employees

### Get specific employee
GET http://localhost:4004/odata/v4/mini/Employees('E001')

### Get all work metrics
GET http://localhost:4004/odata/v4/mini/WorkMetrics

### Get work metrics for specific employee
GET http://localhost:4004/odata/v4/mini/WorkMetrics?$filter=employee_ID eq 'E001'

### Get all burnout metrics
GET http://localhost:4004/odata/v4/mini/BurnoutMetrics

### Get burnout metrics for specific employee
GET http://localhost:4004/odata/v4/mini/BurnoutMetrics?$filter=employee_ID eq 'E001'

### Get employee with work metrics
GET http://localhost:4004/odata/v4/mini/Employees('E001')?$expand=workMetrics

### Get employee with burnout metrics
GET http://localhost:4004/odata/v4/mini/Employees('E001')?$expand=burnoutMetrics

### Get employee with all related data
GET http://localhost:4004/odata/v4/mini/Employees('E001')?$expand=workMetrics,burnoutMetrics

### Create new employee
POST http://localhost:4004/odata/v4/mini/Employees
Content-Type: application/json

{
  "ID": "E011",
  "name": "SAP Mani",
  "department": "IT",
  "role": "Developer"
}

### Update employee
PATCH http://localhost:4004/odata/v4/mini/Employees('E001')
Content-Type: application/json

{
  "department": "Senior Engineering"
}

### Delete employee
DELETE http://localhost:4004/odata/v4/mini/Employees('E011')
